import datetime
import logging
import re
from collections import defaultdict
from decimal import Decimal
from typing import Optional, Dict, List, Tuple, Set
from dataclasses import dataclass
from django.db.models import QuerySet, Q, Max
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.db import transaction, IntegrityError
from django.conf import settings
import time

from port.models import Account, Accounting, Activitate, BondAccrual, CashTransaction, CorporateAction, Custodian, Instrument, Journal, Operation, Partner, SalesTax, Ubo, Trade


logger = logging.getLogger(__name__)

@dataclass
class ImportStats:
    """Statistics for journal import operations"""
    total_processed: int = 0
    successful: int = 0
    failed: int = 0
    skipped: int = 0
    start_time: float = 0
    end_time: float = 0
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []

    @property
    def duration(self) -> float:
        return self.end_time - self.start_time if self.end_time > 0 else 0

    @property
    def success_rate(self) -> float:
        return (self.successful / self.total_processed * 100) if self.total_processed > 0 else 0

@dataclass
class JournalEntryData:
    """Data structure for journal entry creation"""
    ubo: Ubo
    custodian: Custodian
    account: Account
    operation: Operation
    partner: Partner
    instrument: Instrument
    date: datetime.date
    transactionid: str
    value: float
    quantity: float
    details: str

class IbkrJournalStorage:
    try:
        ubo = Ubo.objects.get(ubo_code='DD')
        custodian = Custodian.objects.get(custodian_code='IBKR')
        partner = Partner.objects.get(partner_code='IBKR')
        account = Account.objects.get(account_code='IBKR_USD')
    except ObjectDoesNotExist as e:
        logger.critical(f"Missing a required base object for IbkrJournalStorage: {e}. "
                        "Please ensure UBO 'DD', Custodian 'IBKR', Partner 'IBKR', "
                        "and Account 'IBKR_USD' exist in the database.")
        ubo = None
        custodian = None
        partner = None
        account = None
    except MultipleObjectsReturned as e:
        logger.critical(f"Multiple objects returned for a required base object in IbkrJournalStorage: {e}. "
                        "Please ensure UBO 'DD', Custodian 'IBKR', Partner 'IBKR', "
                        "and Account 'IBKR_USD' have unique codes.")
        ubo = None
        custodian = None
        partner = None
        account = None


    @staticmethod
    def store_journals(date: Optional[datetime.date] = None) -> None:
        """
        Retrieves various financial activities (trades, cash transactions, corporate actions, sales taxes)
        and converts them into journal entries for storage.
        If a date is provided, it filters activities for that specific date.
        Otherwise, it processes all available activities.
        """
        if any(obj is None for obj in [IbkrJournalStorage.ubo, IbkrJournalStorage.custodian,
                                       IbkrJournalStorage.partner, IbkrJournalStorage.account]):
            logger.error(f"Cannot store journals because essential base objects are missing. Values for ubo: {IbkrJournalStorage.ubo}, custodian: {IbkrJournalStorage.custodian}, partner: {IbkrJournalStorage.partner}, account: {IbkrJournalStorage.account}")
            return

        try:
            if date:
                logger.info(f"Fetching financial activities for date: {date}")
                trades = Trade.objects.filter(date=date)
                cash_transactions = CashTransaction.objects.filter(date=date)
                corporate_actions = CorporateAction.objects.filter(date=date)
                sales_taxes = SalesTax.objects.filter(date=date)
            else:
                logger.info("Fetching all financial activities.")
                trades = Trade.objects.all()
                cash_transactions = CashTransaction.objects.all()
                corporate_actions = CorporateAction.objects.all()
                sales_taxes = SalesTax.objects.all()

            journal_entries = []

            # Process different types of activities and extend the journal_entries list
            journal_entries.extend(IbkrJournalStorage.get_commissions_from_trades(trades.exclude(ibcommission=0)))
            journal_entries.extend(IbkrJournalStorage.get_fx_from_trades(trades.filter(assetcategory='CASH')))
            journal_entries.extend(IbkrJournalStorage.get_trades_from_trades(trades.exclude(assetcategory='CASH')))
            journal_entries.extend(IbkrJournalStorage.get_cash_from_cash_transactions(cash_transactions))
            journal_entries.extend(IbkrJournalStorage.get_corporate_actions_from_corporate_actions(corporate_actions))
            journal_entries.extend(IbkrJournalStorage.get_sales_taxes_from_sales_taxes(sales_taxes))
            journal_entries.extend(IbkrJournalStorage.get_profits_from_trades(trades))
            journal_entries.extend(IbkrJournalStorage.get_bond_accruals_for_journal())

            logger.info(f"Generated {len(journal_entries)} journal entries in total.")
            IbkrJournalStorage.bulk_store_journal_entries(journal_entries)

        except Exception as e:
            logger.exception(f"An unexpected error occurred while storing journals for date {date if date else 'all'}.")


    @staticmethod
    def add_journal_entry(
        activity: Activitate,
        operation: Operation,
        account: Account,
        partner: Partner,
        instrument: Instrument,
        value: float,
        quantity: float,
        details: str,
    ) -> Optional[Journal]:
        """
        Creates or retrieves a single journal entry based on provided details.
        Uses get_or_create to prevent duplicate entries based on ubo, custodian, account, and transactionid.
        """
        try:
            # Ensure the UBO for the activity exists
            try:
                activity_ubo = Ubo.objects.get(ubo_code=activity.ubo)
            except ObjectDoesNotExist:
                logger.error(f"UBO with code '{activity.ubo}' not found for activity {activity.transactionid}. Skipping journal entry.")
                return None

            journal_entry, created = Journal.objects.get_or_create(
                ubo=activity_ubo,
                custodian=IbkrJournalStorage.custodian, # Assuming this is always IBKR for this class
                account=account,
                operation=operation,
                partner=partner,
                instrument=instrument,
                date=activity.date,
                transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(activity),
                defaults={
                    "value": value,
                    "quantity": quantity,
                    "details": details,
                }
            )
            if created:
                logger.debug(f"Successfully created journal entry for transaction ID: {activity.transactionid}")
            else:
                logger.info(f"Journal entry already exists for transaction ID: {activity.transactionid}. Updated existing entry.")
                # If not created, update the fields that might have changed
                journal_entry.value = value
                journal_entry.quantity = quantity
                journal_entry.details = details
                journal_entry.save()

            return journal_entry
        except ObjectDoesNotExist as e:
            logger.error(f"Required related object not found when adding journal entry for activity {activity.transactionid}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error adding or updating journal entry for activity {activity.transactionid}: {e}", exc_info=True)
            return None

    @staticmethod
    def bulk_store_journal_entries(journal_entries: List[Journal]) -> ImportStats:
        """
        Performs optimized bulk creation or update of journal entries.
        Uses true bulk operations with conflict resolution for better performance.
        """
        stats = ImportStats(
            total_processed=len(journal_entries),
            start_time=time.time()
        )

        if not journal_entries:
            logger.info("No journal entries to bulk store.")
            stats.end_time = time.time()
            return stats

        try:
            with transaction.atomic():
                # Split into batches for memory efficiency
                batch_size = getattr(settings, 'JOURNAL_IMPORT_BATCH_SIZE', 1000)

                for i in range(0, len(journal_entries), batch_size):
                    batch = journal_entries[i:i + batch_size]
                    batch_stats = IbkrJournalStorage._process_journal_batch(batch)

                    stats.successful += batch_stats.successful
                    stats.failed += batch_stats.failed
                    stats.skipped += batch_stats.skipped
                    stats.errors.extend(batch_stats.errors)

                    logger.info(f"Processed batch {i//batch_size + 1}: "
                              f"{batch_stats.successful} successful, "
                              f"{batch_stats.failed} failed, "
                              f"{batch_stats.skipped} skipped")

            stats.end_time = time.time()
            logger.info(f"Bulk storage completed: {stats.successful}/{stats.total_processed} successful "
                       f"in {stats.duration:.2f}s (success rate: {stats.success_rate:.1f}%)")

            return stats

        except Exception as e:
            stats.end_time = time.time()
            error_msg = f"Critical error during bulk journal storage: {e}"
            logger.error(error_msg, exc_info=True)
            stats.errors.append(error_msg)
            return stats

    @staticmethod
    def _process_journal_batch(batch: List[Journal]) -> ImportStats:
        """Process a single batch of journal entries with optimized bulk operations"""
        batch_stats = ImportStats(total_processed=len(batch))

        try:
            # Extract transaction IDs for existing entries check
            transaction_ids = [j.transactionid for j in batch]
            existing_entries = set(
                Journal.objects.filter(transactionid__in=transaction_ids)
                .values_list('transactionid', flat=True)
            )

            # Separate new entries from updates
            new_entries = []
            update_entries = []

            for journal in batch:
                if journal.transactionid in existing_entries:
                    update_entries.append(journal)
                else:
                    new_entries.append(journal)

            # Bulk create new entries
            if new_entries:
                try:
                    Journal.objects.bulk_create(new_entries, ignore_conflicts=True)
                    batch_stats.successful += len(new_entries)
                    logger.debug(f"Bulk created {len(new_entries)} new journal entries")
                except IntegrityError as e:
                    logger.warning(f"Some entries in batch had conflicts during creation: {e}")
                    batch_stats.failed += len(new_entries)
                    batch_stats.errors.append(f"Bulk create failed: {e}")

            # Handle updates individually (Django doesn't support bulk_update with conflicts well)
            for journal in update_entries:
                try:
                    Journal.objects.filter(transactionid=journal.transactionid).update(
                        operation=journal.operation,
                        partner=journal.partner,
                        instrument=journal.instrument,
                        date=journal.date,
                        value=journal.value,
                        quantity=journal.quantity,
                        details=journal.details,
                    )
                    batch_stats.successful += 1
                except Exception as e:
                    batch_stats.failed += 1
                    error_msg = f"Failed to update journal {journal.transactionid}: {e}"
                    batch_stats.errors.append(error_msg)
                    logger.warning(error_msg)

            return batch_stats

        except Exception as e:
            batch_stats.failed = len(batch)
            error_msg = f"Batch processing failed: {e}"
            batch_stats.errors.append(error_msg)
            logger.error(error_msg, exc_info=True)
            return batch_stats

    @staticmethod
    def get_bond_accruals_for_journal():
        bonds = BondAccrual.objects.filter(custodian=IbkrJournalStorage.custodian)
        journals = []
        if not bonds.exists():
            logger.info("No bonds accruals found.")
            return []

        for bond in bonds:
            transaction_id = f"{bond.instrument.symbol} {bond.operation.operation_name} {bond.date}"
            try:
                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=bond.operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=bond.instrument,
                        date=bond.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(transaction_id),
                        value=bond.value,
                        quantity=bond.quantity,
                        details=bond.details,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing bond accrual {transaction_id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} commission journal entries.")
        return journals


    # Cache for transaction ID checking to avoid repeated database queries
    _transaction_id_cache: Dict[str, int] = {}

    @staticmethod
    def check_transaction_id_in_journal_and_increment_id(transaction_id: str) -> str:
        """
        Optimized transaction ID uniqueness checker with caching.
        Checks if a transaction ID already exists and increments suffix if needed.
        Uses caching to avoid repeated database queries during bulk operations.
        """
        base_id = transaction_id
        original_base_id = base_id

        # Remove any existing numerical suffix like *1, *2 etc.
        base_id = re.sub(r'\*\d+$', '', base_id)

        try:
            # Check cache first
            if base_id in IbkrJournalStorage._transaction_id_cache:
                next_suffix = IbkrJournalStorage._transaction_id_cache[base_id] + 1
                IbkrJournalStorage._transaction_id_cache[base_id] = next_suffix
                new_transaction_id = f"{base_id}*{next_suffix}"
                logger.debug(f"Transaction ID '{original_base_id}' adjusted to '{new_transaction_id}' using cache.")
                return new_transaction_id

            # Query database for existing IDs with this base
            similar_ids = Journal.objects.filter(
                transactionid__startswith=base_id
            ).values_list('transactionid', flat=True)

            suffixes = []
            for tid in similar_ids:
                match = re.match(rf'^{re.escape(base_id)}(?:\*(\d+))?$', tid)
                if match:
                    suffix = match.group(1)
                    suffixes.append(int(suffix) if suffix else 0)

            next_suffix = max(suffixes) + 1 if suffixes else 0

            # Update cache
            IbkrJournalStorage._transaction_id_cache[base_id] = next_suffix

            new_transaction_id = base_id if next_suffix == 0 else f"{base_id}*{next_suffix}"

            if new_transaction_id != original_base_id:
                logger.debug(f"Transaction ID '{original_base_id}' adjusted to '{new_transaction_id}' to ensure uniqueness.")

            return new_transaction_id

        except Exception as e:
            logger.error(f"Error checking or incrementing transaction ID for '{original_base_id}': {e}", exc_info=True)
            return original_base_id

    @staticmethod
    def clear_transaction_id_cache():
        """Clear the transaction ID cache. Useful for testing or memory management."""
        IbkrJournalStorage._transaction_id_cache.clear()
        logger.debug("Transaction ID cache cleared.")

    @staticmethod
    def get_or_create_operation(operation_code: str, value: float, symbol: str) -> Operation:
        """
        Retrieves an existing Operation object or creates a new one if it doesn't exist.
        Includes specific logic for mapping and transforming operation codes.
        """
        original_operation_code = operation_code
        # Standardize operation code by making it uppercase
        operation_code = operation_code.upper()

        operation_mapping = {
            'DEPOSITS/WITHDRAWALS': 'VIRAMENT',
            'BOND INTEREST PAID': 'BOND_INTEREST_PAID_BROKER',
            'BOND INTEREST RECEIVED': 'BOND_INTEREST_RECEIVED_BROKER',
            'WITHHOLDING TAX': 'WHT_BROKER',
            'BROKER INTEREST PAID': 'BROKER_INTEREST_PAID_BROKER',
            'BROKER INTEREST RECEIVED': 'BROKER_INTEREST_RECEIVED',
            'OTHER FEES': 'COMIS_BROKER_VALUTA',
            'TM': 'SELL_BOND',
            'VAT': 'COMIS_BROKER_VALUTA',
            'BUY': 'BUY_BOND_BROKER', # Default, can be overridden below
            'SELL': 'SELL_BOND_BANCA', # Default, can be overridden below
            'DIVIDENDS': 'INC_DIVIDEND_BROKER',
        }

        # Apply specific logic for operation code transformation
        if operation_code.startswith('VIRAMENT') and value >= 0:
            operation_code = 'VIR_INT_IN_BROKER_VALUTA'
        elif operation_code.startswith('VIRAMENT') and value < 0:
            operation_code = 'VIR_INT_OUT_BROKER_VALUTA'
        elif operation_code == "BUY_BOND" and symbol in ['BIMBOA', 'ECNS', 'MCHI', 'VNM']:
            operation_code = 'BUY_STOCK'
        else:
            # Use mapped code if available, otherwise use the original processed code
            operation_code = operation_mapping.get(operation_code, operation_code)

        try:
            return Operation.objects.get(operation_code=operation_code)
        except Operation.DoesNotExist:
            logger.error(f"Operation not found for {operation_code}")
            default_accounting, _ = Accounting.objects.get_or_create(
                account_code='000',
                defaults={
                    'account_name': 'DEFAULT_NO_VALUE',
                    'has_currency': False,
                    'has_custodian_debit': True,
                    'has_custodian_credit': True,
                    'has_partner_debit': True,
                    'has_partner_credit': True,
                    'has_symbol': False,
                    'has_dot': False,
                }
            )

            return Operation.objects.get_or_create(
                operation_code=operation_code,
                defaults={
                    'operation_name': operation_code,
                    'debit': default_accounting,
                    'credit': default_accounting,
                }
            )[0]

    @staticmethod
    def get_or_create_instrument(symbol: str) -> Instrument:
        """
        Retrieves an existing Instrument object or creates a new one if it doesn't exist
        for the given symbol and the class's custodian.
        """
        if not IbkrJournalStorage.custodian or not IbkrJournalStorage.account:
            logger.error(f"Cannot get or create instrument '{symbol}'. Custodian or Account is not initialized.")
            # Fallback or raise an error if essential class attributes are missing
            raise AttributeError("IbkrJournalStorage.custodian or IbkrJournalStorage.account is None.")

        try:
            instrument, created = Instrument.objects.get_or_create(
                symbol=symbol,
                custodian=IbkrJournalStorage.custodian,
                defaults={
                    'name': symbol, # Default name can be the symbol
                    'isin': symbol, # Default ISIN can be the symbol, might need refinement
                    'currency': IbkrJournalStorage.account.currency # Use account's currency as default
                }
            )
            if created:
                logger.info(f"Created new instrument: '{symbol}' for custodian '{IbkrJournalStorage.custodian.custodian_code}'.")
            else:
                logger.debug(f"Retrieved existing instrument: '{symbol}'.")
            return instrument
        except MultipleObjectsReturned:
            logger.error(f"Multiple instruments found for symbol '{symbol}' and custodian '{IbkrJournalStorage.custodian.custodian_code}'. Returning the first one found.")
            return Instrument.objects.filter(symbol=symbol, custodian=IbkrJournalStorage.custodian).first()
        except Exception as e:
            logger.error(f"Error getting or creating instrument '{symbol}': {e}", exc_info=True)
            raise # Re-raise to ensure calling methods are aware of the failure


    @staticmethod
    def get_commissions_from_trades(trades: QuerySet[Trade]) -> list[Journal]:
        """Generates journal entries for commissions from a queryset of trades."""
        journals = []
        if not trades.exists():
            logger.info("No trades with commissions found.")
            return []

        for trade in trades:
            try:
                # Determine symbol for the instrument. Assuming USD for specific pattern, otherwise trade.symbol
                symbol = "USD" if len(trade.symbol) == 7 and trade.symbol[3] == '.' else trade.symbol
                
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping commission journal for trade {trade.id}: essential base objects are missing.")
                    continue

                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code='COMIS_BROKER_VALUTA',
                    value=trade.ibcommission,
                    symbol=symbol
                )
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=symbol)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument,
                        date=trade.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(trade.transaction_id),
                        value=trade.ibcommission,
                        quantity=0,
                        details='commission',
                    )
                )
            except Exception as e:
                logger.error(f"Error processing commission for trade ID {trade.id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} commission journal entries.")
        return journals

    @staticmethod
    def get_fx_from_trades(trades: QuerySet[Trade]) -> list[Journal]:
        """Generates journal entries for FX (currency exchange) from a queryset of cash trades."""
        journals = []
        if not trades.exists():
            logger.info("No FX trades found.")
            return []

        for trade in trades:
            try:
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping FX journal for trade {trade.id}: essential base objects are missing.")
                    continue

                # FX In
                operation_fx_in = IbkrJournalStorage.get_or_create_operation(
                    operation_code='FX_IN_BROKER',
                    value=trade.proceeds,
                    symbol=trade.currency
                )
                instrument_fx_in = IbkrJournalStorage.get_or_create_instrument(symbol=trade.currency)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation_fx_in,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument_fx_in,
                        date=trade.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(trade.transaction_id),
                        value=trade.proceeds,
                        quantity=trade.proceeds, # Quantity for FX often matches value
                        details='fx in',
                    )
                )
                fx_out_symbol = trade.symbol[:3] if len(trade.symbol) >= 3 else trade.symbol
                operation_fx_out = IbkrJournalStorage.get_or_create_operation(
                    operation_code='FX_OUT_BROKER',
                    value=trade.quantity, # Quantity of the currency being sold/converted out
                    symbol=fx_out_symbol
                )
                instrument_fx_out = IbkrJournalStorage.get_or_create_instrument(symbol=fx_out_symbol)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation_fx_out,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument_fx_out,
                        date=trade.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(trade.transaction_id),
                        value=trade.quantity,
                        quantity=trade.quantity,
                        details='fx out',
                    )
                )
            except Exception as e:
                logger.error(f"Error processing FX for trade ID {trade.id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} FX journal entries.")
        return journals

    @staticmethod
    def get_trades_from_trades(trades: QuerySet[Trade]) -> list[Journal]:
        """Generates journal entries for regular asset trades (buy/sell) from a queryset of trades."""
        journals = []
        if not trades.exists():
            logger.info("No asset trades found.")
            return []

        for trade in trades:
            try:
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping trade journal for trade {trade.id}: essential base objects are missing.")
                    continue

                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code=trade.buysell,
                    value=trade.proceeds,
                    symbol=trade.symbol
                )
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=trade.symbol)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument,
                        date=trade.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(trade.transaction_id),
                        value=trade.proceeds,
                        quantity=trade.quantity,
                        details='exchange trade',
                    )
                )
            except Exception as e:
                logger.error(f"Error processing asset trade ID {trade.id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} asset trade journal entries.")
        return journals

    @staticmethod
    def get_cash_from_cash_transactions(cash_transactions: QuerySet[CashTransaction]) -> list[Journal]:
        """Generates journal entries from a queryset of CashTransaction objects."""
        journals = []
        if not cash_transactions.exists():
            logger.info("No cash transactions found.")
            return []

        for cash_transaction in cash_transactions:
            try:
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping cash transaction journal for {cash_transaction.transaction_id}: essential base objects are missing.")
                    continue

                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code=cash_transaction.operation,
                    value=cash_transaction.value,
                    symbol=cash_transaction.symbol
                )
                symbol = cash_transaction.symbol if cash_transaction.symbol and cash_transaction.symbol != "nan" else cash_transaction.currency.currency_code
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=symbol)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument,
                        date=cash_transaction.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(cash_transaction.transaction_id),
                        value=cash_transaction.value,
                        quantity=0, # Cash transactions usually don't involve quantity of an instrument
                        details=cash_transaction.details,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing cash transaction ID {cash_transaction.transaction_id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} cash transaction journal entries.")
        return journals

    @staticmethod
    def get_corporate_actions_from_corporate_actions(corporate_actions: QuerySet[CorporateAction]) -> list[Journal]:
        """Generates journal entries from a queryset of CorporateAction objects."""
        journals = []
        if not corporate_actions.exists():
            logger.info("No corporate actions found.")
            return []

        for corporate_action in corporate_actions:
            try:
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping corporate action journal for {corporate_action.transaction_id}: essential base objects are missing.")
                    continue

                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code=corporate_action.operation,
                    value=corporate_action.value,
                    symbol=corporate_action.symbol
                )
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=corporate_action.symbol)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument,
                        date=corporate_action.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(corporate_action.transaction_id),
                        value=corporate_action.value,
                        quantity=corporate_action.quantity,
                        details=corporate_action.details,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing corporate action ID {corporate_action.transaction_id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} corporate action journal entries.")
        return journals

    @staticmethod
    def get_sales_taxes_from_sales_taxes(sales_taxes: QuerySet[SalesTax]) -> list[Journal]:
        """Generates journal entries from a queryset of SalesTax objects."""
        journals = []
        if not sales_taxes.exists():
            logger.info("No sales taxes found.")
            return []

        for sales_tax in sales_taxes:
            try:
                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping sales tax journal for {sales_tax.transaction_id}: essential base objects are missing.")
                    continue

                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code=sales_tax.operation,
                    value=sales_tax.value,
                    symbol=sales_tax.currency.currency_code # Use currency code as symbol for instrument
                )
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=sales_tax.currency.currency_code)

                journals.append(
                    Journal(
                        ubo=IbkrJournalStorage.ubo,
                        custodian=IbkrJournalStorage.custodian,
                        account=IbkrJournalStorage.account,
                        operation=operation,
                        partner=IbkrJournalStorage.partner,
                        instrument=instrument,
                        date=sales_tax.date,
                        transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(sales_tax.transaction_id),
                        value=sales_tax.value,
                        quantity=0, # Sales tax usually doesn't involve quantity of an instrument
                        details=sales_tax.details,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing sales tax ID {sales_tax.transaction_id}: {e}", exc_info=True)
        logger.info(f"Generated {len(journals)} sales tax journal entries.")
        return journals

    @staticmethod
    def get_profits_from_trades(trades_queryset: QuerySet[Trade]) -> list[Journal]:
        if not trades_queryset.exists():
            logger.info("No trades to calculate profits from.")
            return []

        # Filter for BUY/SELL operations and order by date and transaction_id for consistent FIFO calculation
        trades_queryset = trades_queryset.filter(operation__in=['BUY', 'SELL']).order_by('date', 'transaction_id')
        if not trades_queryset.exists():
            logger.info("No BUY or SELL trades found to calculate profits from.")
            return []

        trades_symbols = trades_queryset.values_list('symbol', flat=True).distinct()
        trade_profits_data = {} # Stores calculated profit data per trade ID

        for symbol in trades_symbols:
            value_at_cost = Decimal('0.0')
            latest_cost = Decimal('0.0')
            cumulated_value = Decimal('0.0')
            cumulated_quantity = Decimal('0.0')
            
            # Fetch trades for the current symbol, ordered by date and then transaction_id
            symbol_trades = trades_queryset.filter(symbol=symbol).order_by('date', 'transaction_id')

            for trade in symbol_trades:
                try:
                    profit = Decimal('0.0')
                    
                    if trade.quantity >= 0:  # This is a BUY or a positive adjustment to holding
                        value_at_cost = -trade.proceeds # Cost of acquisition
                        cumulated_value += -trade.proceeds
                        cumulated_quantity += trade.quantity
                        # Update latest_cost for subsequent sells
                        latest_cost = cumulated_value / cumulated_quantity if cumulated_quantity != 0 else Decimal('0.0')
                    else:  # This is a SELL (negative quantity)
                        if cumulated_quantity == 0:
                            logger.warning(f"Attempting to sell '{trade.symbol}' (Trade ID: {trade.id}) with zero cumulated quantity. Profit calculation may be inaccurate.")
                            # In this case, profit cannot be accurately calculated using FIFO.
                            # We might want to assign 0 profit or handle it as an error.
                            profit = Decimal('0.0')
                            value_at_cost = Decimal('0.0')
                        else:
                            # Calculate cost of goods sold (COGS) based on the latest average cost
                            cost = abs(trade.quantity) * latest_cost
                            value_at_cost = cost # This is the value of sold quantity at its cost basis
                            cumulated_quantity += trade.quantity # quantity is negative for sell, so it subtracts
                            cumulated_value += cost # Subtract the cost basis of sold items from cumulated value

                            # Update latest_cost after the sale
                            latest_cost = cumulated_value / cumulated_quantity if cumulated_quantity != 0 else Decimal('0.0')
                            
                            # Profit = Selling Proceeds - Cost of Goods Sold
                            profit = trade.proceeds + value_at_cost # trade.proceeds is positive for sell, value_at_cost is positive for cost

                    trade_profits_data[trade.id] = {
                        'profit': profit,
                        'date': trade.date,
                        'symbol': trade.symbol,
                        'transaction_id': trade.transaction_id,
                    }
                except Decimal.InvalidOperation as de:
                    logger.error(f"Decimal calculation error for trade ID {trade.id}, symbol {trade.symbol}: {de}")
                except Exception as e:
                    logger.error(f"Error calculating profit for trade ID {trade.id}, symbol {trade.symbol}: {e}", exc_info=True)

        journal_entries = []
        for trade_id, values in trade_profits_data.items():
            try:
                profit = values['profit']
                if profit == Decimal('0.0'):
                    logger.debug(f"Skipping profit journal for trade ID {trade_id} as profit is zero.")
                    continue

                # Check for necessary objects before creating Journal
                if not all([IbkrJournalStorage.ubo, IbkrJournalStorage.custodian, IbkrJournalStorage.account, IbkrJournalStorage.partner]):
                    logger.error(f"Skipping profit journal for trade {trade_id}: essential base objects are missing.")
                    continue

                operation_code = 'PROFIT_INVESTITII' if profit > 0 else 'PIERDERE_INVESTITII'
                operation = IbkrJournalStorage.get_or_create_operation(
                    operation_code=operation_code,
                    value=float(profit), # Convert Decimal to float for value field
                    symbol=values['symbol']
                )
                instrument = IbkrJournalStorage.get_or_create_instrument(symbol=values['symbol'])

                journal_entries.append(Journal(
                    ubo=IbkrJournalStorage.ubo,
                    custodian=IbkrJournalStorage.custodian,
                    account=IbkrJournalStorage.account,
                    operation=operation,
                    partner=IbkrJournalStorage.partner,
                    instrument=instrument,
                    date=values['date'],
                    transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(
                        # Create a dummy object or ensure the original trade object is passed if transaction_id is on it.
                        # Assuming 'transaction_id' is directly accessible from the 'values' dict or a dummy Trade can be made.
                        type('Trade', (object,), {'transaction_id': values['transaction_id']})()
                    ),
                    value=float(profit), # Convert Decimal to float for value field
                    quantity=0, # Profit/loss entries usually don't have quantity
                    details='profit' if profit > 0 else 'loss',
                ))
            except Exception as e:
                logger.error(f"Error creating journal entry for profit/loss of trade ID {trade_id}: {e}", exc_info=True)

        logger.info(f"Generated {len(journal_entries)} profit/loss journal entries.")
        return journal_entries
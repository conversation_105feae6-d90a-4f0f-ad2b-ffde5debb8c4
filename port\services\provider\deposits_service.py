import logging
from typing import List, Dict, Set, Tuple
from collections import defaultdict
from datetime import date, timedelta
from dateutil.relativedelta import relativedelta
from django.db.models import QuerySet
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from port.models import (
    Bnr, Currency, Deposits, Journal, Operation, Instrument,
    Ubo, Custodian, Account, Partner
)

logger = logging.getLogger(__name__)


class DepositsService:
    def __init__(self, ubo=None):
        """Initialize the service with required base objects and caching"""
        try:
            if not ubo:
                self.ubo = Ubo.objects.get(ubo_code='DD')
            else:
                self.ubo = ubo
        except ObjectDoesNotExist:
            logger.error("UBO 'DD' not found. Please create it first.")
            self.ubo = None
        except MultipleObjectsReturned:
            logger.error("Multiple UBOs with code 'DD' found. Please ensure uniqueness.")
            self.ubo = None

        # Performance optimization caches
        self.batch_size = getattr(settings, 'DEPOSITS_BATCH_SIZE', 500)
        self.bnr_cache: Dict[Tuple[str, date], float] = {}  # (currency, date) -> rate
        self.currency_rates_cache: Dict[str, Dict[date, float]] = {}  # currency -> {date: rate}
        self.object_cache = {
            'currencies': {},
            'operations': {},
            'accounts': {},
            'partners': {},
            'custodians': {},
            'instruments': {}
        }

    def calculate_and_store_accruals(self) -> Dict:
        if not self.ubo:
            raise ValueError("Cannot process deposits without valid UBO")

        logger.info("Starting optimized deposit accruals calculation...")

        # Pre-load and cache all required data
        self._preload_caches()

        # Get deposits to process
        deposits_queryset = self._get_deposits_queryset()
        total_deposits = deposits_queryset.count()

        logger.info(f"Processing {total_deposits} deposits in batches of {self.batch_size}")

        results = {
            'processed_deposits': 0,
            'created_journals': 0,
            'errors': [],
            'deposit_results': []
        }

        # Process deposits in batches for better performance
        all_journal_entries = []

        for i, deposit in enumerate(deposits_queryset):
            try:
                # Log progress every 100 deposits
                if i % 100 == 0 and i > 0:
                    logger.info(f"Processed {i}/{total_deposits} deposits ({i/total_deposits*100:.1f}%)")

                deposit_info = self._extract_deposit_info(deposit)
                accrual_entries = self._calculate_accruals_for_deposit_optimized(deposit_info)

                # Collect entries for bulk creation
                all_journal_entries.extend(accrual_entries)
                results['processed_deposits'] += 1

                results['deposit_results'].append({
                    'deposit_id': deposit_info['deposit_id'],
                    'custodian': deposit_info['custodian'],
                    'entries_planned': len(accrual_entries)
                })

                # Bulk create when batch is full
                if len(all_journal_entries) >= self.batch_size:
                    created_count = self._bulk_store_journal_entries(all_journal_entries)
                    results['created_journals'] += created_count
                    all_journal_entries = []

            except Exception as e:
                error_msg = f"Error processing deposit {deposit.deposit.symbol}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                results['errors'].append(error_msg)

        # Store remaining entries
        if all_journal_entries:
            created_count = self._bulk_store_journal_entries(all_journal_entries)
            results['created_journals'] += created_count

        logger.info(f"Completed optimized deposit accruals calculation. "
                   f"Processed: {results['processed_deposits']}, "
                   f"Created: {results['created_journals']}, "
                   f"Errors: {len(results['errors'])}")

        return results

    def _preload_caches(self):
        """Pre-load all required data into caches for performance optimization"""
        logger.info("Pre-loading caches for optimized processing...")

        # Load BNR rates into cache
        bnr_rates = self._get_bnr_rates()
        for bnr in bnr_rates:
            currency_code = bnr.currency_code.currency_code
            if currency_code not in self.currency_rates_cache:
                self.currency_rates_cache[currency_code] = {}
            self.currency_rates_cache[currency_code][bnr.date] = bnr.value_exact

        # Pre-load common objects
        self._preload_currencies()
        self._preload_operations()
        self._preload_accounts()
        self._preload_partners()
        self._preload_custodians()

        logger.info(f"Caches loaded: {len(self.currency_rates_cache)} currencies, "
                   f"{len(self.object_cache['operations'])} operations")

    def _preload_currencies(self):
        """Pre-load currency objects"""
        for currency in Currency.objects.all():
            self.object_cache['currencies'][currency.currency_code] = currency

    def _preload_operations(self):
        """Pre-load operation objects"""
        operation_codes = [
            'CONSTITUIRE_DEP_VALUTA', 'CONSTITUIRE_DEP_LEI',
            'ACCRUAL_DEP_VALUTA', 'ACCRUAL_DEP_LEI',
            'FX_DEP_VALUTA', 'LICHIDARE_DEP_VALUTA', 'LICHIDARE_DEP_LEI'
        ]
        for operation in Operation.objects.filter(operation_code__in=operation_codes):
            self.object_cache['operations'][operation.operation_code] = operation

    def _preload_accounts(self):
        """Pre-load account objects"""
        for account in Account.objects.all():
            self.object_cache['accounts'][account.account_code] = account

    def _preload_partners(self):
        """Pre-load partner objects"""
        for partner in Partner.objects.all():
            self.object_cache['partners'][partner.partner_code] = partner

    def _preload_custodians(self):
        """Pre-load custodian objects"""
        for custodian in Custodian.objects.all():
            self.object_cache['custodians'][custodian.custodian_code] = custodian

    def _get_bnr_rates(self) -> QuerySet:
        """Get BNR exchange rates using Django ORM"""
        return Bnr.objects.select_related('currency_code').filter(
            value_exact__isnull=False
        ).order_by('date')

    def _get_deposits_queryset(self, deposit_symbol: str = None) -> QuerySet:
        """Get deposits queryset with related objects"""
        queryset = Deposits.objects.select_related(
            'deposit__currency',
            'deposit__custodian'
        )

        if deposit_symbol:
            queryset = queryset.filter(deposit__symbol=deposit_symbol)

        return queryset

    def _process_single_deposit(self, deposit: Deposits, bnr_rates: QuerySet) -> Dict:
        """Process a single deposit and create journal entries"""
        deposit_info = self._extract_deposit_info(deposit)

        # Calculate accruals
        accrual_entries = self._calculate_accruals_for_deposit(deposit_info, bnr_rates)

        # Create journal entries
        processed_journals = self._create_journal_entries(accrual_entries, deposit)

        return {
            'deposit_symbol': deposit.deposit.symbol,
            'custodian': deposit.deposit.custodian.custodian_code,
            'currency': deposit.deposit.currency.currency_code,
            'accrual_entries': len(accrual_entries),
            'created_journals': processed_journals,
            'entries': accrual_entries
        }

    def _extract_deposit_info(self, deposit: Deposits) -> Dict:
        """Extract deposit information into a dictionary"""
        return {
            'principal': float(deposit.principal),
            'interest_rate': float(deposit.interest_rate),
            'convention': int(deposit.convention) if deposit.convention in ['360', '365'] else 365,
            'start_date': deposit.start,
            'end_date': deposit.maturity,
            'currency': deposit.deposit.currency.currency_code,
            'custodian': deposit.deposit.custodian.custodian_code,
            'deposit_id': deposit.deposit.symbol,
            'incasat': float(deposit.interest_amount or 0),
        }

    def _calculate_accruals_for_deposit(self, deposit_info: Dict, bnr_rates: QuerySet) -> List[Dict]:
        """Calculate accruals and FX differences for a deposit using Django ORM"""
        principal = deposit_info['principal']
        interest_rate = deposit_info['interest_rate'] / 100
        convention = deposit_info['convention']
        start_date = deposit_info['start_date']
        end_date = deposit_info['end_date']
        currency = deposit_info['currency']
        deposit_id = deposit_info['deposit_id']
        custodian = deposit_info['custodian']
        incasat = deposit_info['incasat']

        # Calculate end of months between start_date and minimum of today and end_date
        today = timezone.now().date()
        max_date = today.replace(day=1)  # First day of current month

        # Generate end of month dates
        end_of_months = self._generate_end_of_months(start_date, min(max_date, end_date))
        end_of_months = [x for x in end_of_months if start_date <= x <= end_date]

        logger.debug(f"{custodian} {deposit_id} end_of_months: {end_of_months}")

        # Get BNR rates for this currency
        currency_rates = self._get_currency_rates(bnr_rates, currency)

        # Initialize result list
        results = []

        # Step 1: CONSTITUIRE_DEP_VALUTA/LEI
        initial_bnr_rate = self._get_bnr_rate_before_date(currency_rates, start_date)
        operation_code = 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI'
        initial_value_ron = principal * initial_bnr_rate

        results.append({
            'date': start_date,
            'operation': operation_code,
            'value': -principal,
            'currency': currency,
            'bnr': initial_bnr_rate,
            'value_ron': -initial_value_ron,
            'quantity': principal,
            'transactionid': f"{deposit_id} CONSTITUIRE {start_date}",
            'storno': False
        })

        # Step 2: Calculate monthly accruals
        accrual_results = self._calculate_monthly_accruals(
            end_of_months, start_date, principal, interest_rate, convention,
            currency, deposit_id, currency_rates, initial_bnr_rate
        )
        results.extend(accrual_results['entries'])

        # Step 3: Handle maturity if deposit has ended
        if end_date <= max_date:
            maturity_results = self._calculate_maturity_entries(
                end_date, accrual_results['last_accrual_date'], principal, interest_rate,
                convention, currency, deposit_id, currency_rates, incasat,
                accrual_results['accrued_interest_cumulative'],
                accrual_results['accrued_interest_cumulative_ron'],
                accrual_results['accrued_fx_cumulative_ron'],
                accrual_results['last_bnr_rate_end']
            )
            results.extend(maturity_results)

        # Add metadata to each entry
        for entry in results:
            entry['custodian'] = custodian
            entry['ubo'] = "DD"
            entry['partner'] = custodian
            entry['account'] = f"{custodian}_{entry['currency']}"
            entry['instrument'] = f"{custodian}_{deposit_id}"
            entry['details'] = f"{deposit_id} {entry['operation']}"

            if entry.get('storno'):
                entry['details'] = f"STORNO {entry['details']}"

            # Round values
            entry['value'] = round(entry['value'], 2)
            entry['value_ron'] = round(entry['value_ron'], 2)
            entry['quantity'] = entry.get('quantity', 0)

        return results

    def _calculate_accruals_for_deposit_optimized(self, deposit_info: Dict) -> List[Dict]:
        """Optimized version using cached data"""
        principal = deposit_info['principal']
        interest_rate = deposit_info['interest_rate'] / 100
        convention = deposit_info['convention']
        start_date = deposit_info['start_date']
        end_date = deposit_info['end_date']
        currency = deposit_info['currency']
        deposit_id = deposit_info['deposit_id']
        custodian = deposit_info['custodian']
        incasat = deposit_info['incasat']

        # Calculate end of months between start_date and minimum of today and end_date
        today = timezone.now().date()
        max_date = today.replace(day=1)  # First day of current month

        # Generate end of month dates
        end_of_months = self._generate_end_of_months(start_date, min(max_date, end_date))
        end_of_months = [x for x in end_of_months if start_date <= x <= end_date]

        # Get cached currency rates
        currency_rates = self.currency_rates_cache.get(currency, {})
        if not currency_rates and currency != 'RON':
            logger.warning(f"No cached rates found for currency {currency}")
            return []

        # Initialize result list
        results = []

        # Step 1: CONSTITUIRE_DEP_VALUTA/LEI
        initial_bnr_rate = self._get_cached_bnr_rate(currency, start_date)
        operation_code = 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI'
        initial_value_ron = principal * initial_bnr_rate

        results.append({
            'date': start_date,
            'operation': operation_code,
            'value': -principal,
            'currency': currency,
            'bnr': initial_bnr_rate,
            'value_ron': -initial_value_ron,
            'quantity': principal,
            'transactionid': f"{deposit_id} CONSTITUIRE {start_date}",
            'storno': False
        })

        # Step 2: Calculate monthly accruals
        accrual_results = self._calculate_monthly_accruals_optimized(
            end_of_months, start_date, principal, interest_rate, convention,
            currency, deposit_id, initial_bnr_rate
        )
        results.extend(accrual_results['entries'])

        # Step 3: Handle maturity if deposit has ended
        if end_date <= max_date:
            maturity_results = self._calculate_maturity_entries_optimized(
                end_date, accrual_results['last_accrual_date'], principal, interest_rate,
                convention, currency, deposit_id, currency_rates, incasat,
                accrual_results['accrued_interest_cumulative'],
                accrual_results['accrued_interest_cumulative_ron'],
                accrual_results['accrued_fx_cumulative_ron'],
                accrual_results['last_bnr_rate_end']
            )
            results.extend(maturity_results)

        # Add metadata to each entry
        for entry in results:
            entry['custodian'] = custodian
            entry['ubo'] = "DD"
            entry['partner'] = custodian
            entry['account'] = f"{custodian}_{entry['currency']}"
            entry['instrument'] = f"{custodian}_{deposit_id}"
            entry['details'] = f"{deposit_id} {entry['operation']}"

            if entry.get('storno'):
                entry['details'] = f"STORNO {entry['details']}"

            # Round values
            entry['value'] = round(entry['value'], 2)
            entry['value_ron'] = round(entry['value_ron'], 2)
            entry['quantity'] = entry.get('quantity', 0)

        return results

    def _generate_end_of_months(self, start_date, end_date):
        """Generate list of end-of-month dates between start_date and end_date"""
        from calendar import monthrange

        end_of_months = []
        current_date = start_date.replace(day=1)  # First day of start month

        while current_date <= end_date:
            # Get last day of current month
            last_day = monthrange(current_date.year, current_date.month)[1]
            end_of_month = current_date.replace(day=last_day)

            if end_of_month >= start_date:
                end_of_months.append(end_of_month)

            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return end_of_months

    def _get_currency_rates(self, bnr_rates: QuerySet, currency: str) -> Dict:
        """Get BNR rates for a specific currency"""
        if currency == 'RON':
            # For RON, rate is always 1
            return {rate.date: 1.0 for rate in bnr_rates}

        currency_rates = {}
        for rate in bnr_rates.filter(currency_code__currency_code=currency):
            currency_rates[rate.date] = float(rate.value_exact)

        return currency_rates

    def _get_bnr_rate_before_date(self, currency_rates: Dict, target_date) -> float:
        """Get the BNR rate for the last available date before or on target_date"""
        available_dates = [date for date in currency_rates.keys() if date <= target_date]

        if not available_dates:
            # If no rate available before target_date, use the first available rate
            if currency_rates:
                return list(currency_rates.values())[0]
            return 1.0  # Default to 1 if no rates available

        latest_date = max(available_dates)
        return currency_rates[latest_date]

    def _calculate_monthly_accruals(self, end_of_months, start_date, principal, interest_rate,
                                  convention, currency, deposit_id, currency_rates, initial_bnr_rate):
        """Calculate monthly accrual entries"""
        results = []
        accrued_interest_cumulative = 0
        accrued_interest_cumulative_ron = 0
        accrued_fx_cumulative_ron = 0
        last_accrual_date = start_date
        last_bnr_rate_end = initial_bnr_rate

        for end_of_month in end_of_months:
            # Calculate days for accrual
            days = (end_of_month - last_accrual_date).days

            # Calculate interest accrual
            interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += interest_accrual

            # Get BNR rate at end of month
            bnr_rate_end = self._get_bnr_rate_before_date(currency_rates, end_of_month)

            # Calculate RON values
            interest_accrual_ron = interest_accrual * bnr_rate_end
            accrued_interest_cumulative_ron += interest_accrual_ron

            # Calculate FX difference on principal
            fx_diff_principal = principal * (bnr_rate_end - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_principal

            # Add accrual entry
            results.append({
                'date': end_of_month,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_end,
                'value_ron': interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL {end_of_month}",
                'storno': False
            })

            # Add FX difference entry if significant and currency is not RON
            if currency != 'RON' and abs(fx_diff_principal) > 0.01:
                results.append({
                    'date': end_of_month,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_end,
                    'value_ron': fx_diff_principal,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX {end_of_month}",
                    'storno': False
                })

            last_accrual_date = end_of_month
            last_bnr_rate_end = bnr_rate_end

        return {
            'entries': results,
            'accrued_interest_cumulative': accrued_interest_cumulative,
            'accrued_interest_cumulative_ron': accrued_interest_cumulative_ron,
            'accrued_fx_cumulative_ron': accrued_fx_cumulative_ron,
            'last_accrual_date': last_accrual_date,
            'last_bnr_rate_end': last_bnr_rate_end
        }

    def _get_cached_bnr_rate(self, currency: str, target_date: date) -> float:
        """Get BNR rate using cache for performance"""
        if currency == 'RON':
            return 1.0

        cache_key = (currency, target_date)
        if cache_key in self.bnr_cache:
            return self.bnr_cache[cache_key]

        # Find rate from cached currency rates
        currency_rates = self.currency_rates_cache.get(currency, {})
        available_dates = [d for d in currency_rates.keys() if d <= target_date]

        if not available_dates:
            rate = 1.0
        else:
            rate = currency_rates[max(available_dates)]

        # Cache the result
        self.bnr_cache[cache_key] = rate
        return rate

    def _calculate_monthly_accruals_optimized(self, end_of_months, start_date, principal, interest_rate,
                                            convention, currency, deposit_id, initial_bnr_rate):
        """Optimized monthly accrual calculation using cached data"""
        results = []
        accrued_interest_cumulative = 0
        accrued_interest_cumulative_ron = 0
        accrued_fx_cumulative_ron = 0
        last_accrual_date = start_date
        last_bnr_rate_end = initial_bnr_rate

        for end_of_month in end_of_months:
            # Calculate days for accrual
            days = (end_of_month - last_accrual_date).days

            # Calculate interest accrual
            interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += interest_accrual

            # Get BNR rate at end of month using cache
            bnr_rate_end = self._get_cached_bnr_rate(currency, end_of_month)

            # Calculate RON values
            interest_accrual_ron = interest_accrual * bnr_rate_end
            accrued_interest_cumulative_ron += interest_accrual_ron

            # Calculate FX difference on principal
            fx_diff_principal = principal * (bnr_rate_end - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_principal

            # Add accrual entry
            results.append({
                'date': end_of_month,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_end,
                'value_ron': interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL {end_of_month}",
                'storno': False
            })

            # Add FX difference entry if significant and currency is not RON
            if currency != 'RON' and abs(fx_diff_principal) > 0.01:
                results.append({
                    'date': end_of_month,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_end,
                    'value_ron': fx_diff_principal,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX {end_of_month}",
                    'storno': False
                })

            last_accrual_date = end_of_month
            last_bnr_rate_end = bnr_rate_end

        return {
            'entries': results,
            'accrued_interest_cumulative': accrued_interest_cumulative,
            'accrued_interest_cumulative_ron': accrued_interest_cumulative_ron,
            'accrued_fx_cumulative_ron': accrued_fx_cumulative_ron,
            'last_accrual_date': last_accrual_date,
            'last_bnr_rate_end': last_bnr_rate_end
        }

    def _calculate_maturity_entries(self, end_date, last_accrual_date, principal, interest_rate,
                                  convention, currency, deposit_id, currency_rates, incasat,
                                  accrued_interest_cumulative, accrued_interest_cumulative_ron,
                                  accrued_fx_cumulative_ron, last_bnr_rate_end):
        """Calculate maturity entries for deposit liquidation"""
        results = []

        # Calculate final accrual if needed
        if end_date > last_accrual_date:
            days = (end_date - last_accrual_date).days
            final_interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += final_interest_accrual

            # Get BNR rate at maturity
            bnr_rate_maturity = self._get_bnr_rate_before_date(currency_rates, end_date)
            final_interest_accrual_ron = final_interest_accrual * bnr_rate_maturity
            accrued_interest_cumulative_ron += final_interest_accrual_ron

            # Calculate final FX difference on principal
            fx_diff_final = principal * (bnr_rate_maturity - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_final

            # Add final accrual entry
            results.append({
                'date': end_date,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': final_interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_maturity,
                'value_ron': final_interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL FINAL {end_date}",
                'storno': False
            })

            # Add final FX difference if significant
            if currency != 'RON' and abs(fx_diff_final) > 0.01:
                results.append({
                    'date': end_date,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_maturity,
                    'value_ron': fx_diff_final,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX FINAL {end_date}",
                    'storno': False
                })

            last_bnr_rate_end = bnr_rate_maturity

        # Calculate difference between calculated and actual interest
        interest_difference = accrued_interest_cumulative - incasat

        # Add interest collection entry
        if incasat > 0:
            results.append({
                'date': end_date,
                'operation': 'INCASARE_DOB_VALUTA' if currency != 'RON' else 'INCASARE_DOB_LEI',
                'value': incasat,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': incasat * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} INCASARE DOBANDA {end_date}",
                'storno': False
            })

        # Add interest difference adjustment if significant
        if abs(interest_difference) > 0.01:
            results.append({
                'date': end_date,
                'operation': 'AJUSTARE_DOB_VALUTA' if currency != 'RON' else 'AJUSTARE_DOB_LEI',
                'value': -interest_difference,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': -interest_difference * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} AJUSTARE DOBANDA {end_date}",
                'storno': False
            })

        # Add principal liquidation entry
        results.append({
            'date': end_date,
            'operation': 'LICHIDARE_DEP_VALUTA' if currency != 'RON' else 'LICHIDARE_DEP_LEI',
            'value': principal,
            'currency': currency,
            'bnr': last_bnr_rate_end,
            'value_ron': principal * last_bnr_rate_end,
            'quantity': -principal,
            'transactionid': f"{deposit_id} LICHIDARE {end_date}",
            'storno': False
        })

        return results

    def _calculate_maturity_entries_optimized(self, end_date, last_accrual_date, principal, interest_rate,
                                            convention, currency, deposit_id, currency_rates, incasat,
                                            accrued_interest_cumulative, accrued_interest_cumulative_ron,
                                            accrued_fx_cumulative_ron, last_bnr_rate_end):
        """Optimized maturity entries calculation using cached data"""
        results = []

        # Calculate final accrual if needed
        if end_date > last_accrual_date:
            days = (end_date - last_accrual_date).days
            final_interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += final_interest_accrual

            # Get BNR rate at maturity using cache
            bnr_rate_maturity = self._get_cached_bnr_rate(currency, end_date)
            final_interest_accrual_ron = final_interest_accrual * bnr_rate_maturity
            accrued_interest_cumulative_ron += final_interest_accrual_ron

            # Calculate final FX difference on principal
            fx_diff_final = principal * (bnr_rate_maturity - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_final

            # Add final accrual entry
            results.append({
                'date': end_date,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': final_interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_maturity,
                'value_ron': final_interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL FINAL {end_date}",
                'storno': False
            })

            # Add final FX difference entry if significant
            if currency != 'RON' and abs(fx_diff_final) > 0.01:
                results.append({
                    'date': end_date,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_maturity,
                    'value_ron': fx_diff_final,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX FINAL {end_date}",
                    'storno': False
                })

            last_bnr_rate_end = bnr_rate_maturity

        # Calculate interest difference
        interest_difference = accrued_interest_cumulative - incasat

        # Add interest collection entry if incasat > 0
        if incasat > 0:
            results.append({
                'date': end_date,
                'operation': 'INCASARE_DOB_VALUTA' if currency != 'RON' else 'INCASARE_DOB_LEI',
                'value': incasat,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': incasat * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} INCASARE DOBANDA {end_date}",
                'storno': False
            })

        # Add interest difference adjustment if significant
        if abs(interest_difference) > 0.01:
            results.append({
                'date': end_date,
                'operation': 'AJUSTARE_DOB_VALUTA' if currency != 'RON' else 'AJUSTARE_DOB_LEI',
                'value': -interest_difference,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': -interest_difference * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} AJUSTARE DOBANDA {end_date}",
                'storno': False
            })

        # Add principal liquidation entry
        results.append({
            'date': end_date,
            'operation': 'LICHIDARE_DEP_VALUTA' if currency != 'RON' else 'LICHIDARE_DEP_LEI',
            'value': principal,
            'currency': currency,
            'bnr': last_bnr_rate_end,
            'value_ron': principal * last_bnr_rate_end,
            'quantity': -principal,
            'transactionid': f"{deposit_id} LICHIDARE {end_date}",
            'storno': False
        })

        return results

    def _bulk_store_journal_entries(self, journal_entries: List[Dict]) -> int:
        """Bulk create journal entries for better performance"""
        if not journal_entries:
            return 0

        created_count = 0

        try:
            with transaction.atomic():
                # Use update_or_create for each entry to handle duplicates
                for entry_data in journal_entries:
                    try:
                        # Get cached objects
                        operation = self._get_cached_operation(entry_data['operation'])
                        currency_obj = self._get_cached_currency(entry_data['currency'])
                        custodian = self._get_cached_custodian(entry_data['custodian'])
                        partner = self._get_cached_partner(entry_data['partner'])
                        account = self._get_cached_account(entry_data['account'])
                        instrument = self._get_cached_instrument(entry_data['instrument'], currency_obj, custodian)

                        # Use update_or_create to handle potential duplicates
                        journal_obj, created = Journal.objects.update_or_create(
                            ubo=self.ubo,
                            custodian=custodian,
                            account=account,
                            operation=operation,
                            partner=partner,
                            instrument=instrument,
                            date=entry_data['date'],
                            transactionid=entry_data['transactionid'],
                            defaults={
                                'value': float(entry_data['value']),
                                'value_ron': float(entry_data['value_ron']),
                                'bnr': float(entry_data['bnr']),
                                'quantity': float(entry_data['quantity']),
                                'details': entry_data['details'],
                                'storno': entry_data.get('storno', False)
                            }
                        )

                        if created:
                            created_count += 1

                    except Exception as e:
                        logger.error(f"Error creating journal entry {entry_data.get('transactionid', 'unknown')}: {e}")

                logger.info(f"Created/updated {created_count} journal entries")

        except Exception as e:
            logger.error(f"Error in bulk journal creation: {e}", exc_info=True)
            raise

        return created_count

    def _get_cached_operation(self, operation_code: str) -> Operation:
        """Get operation from cache or database"""
        if operation_code not in self.object_cache['operations']:
            try:
                operation = Operation.objects.get(operation_code=operation_code)
                self.object_cache['operations'][operation_code] = operation
            except Operation.DoesNotExist:
                logger.error(f"Operation {operation_code} not found")
                raise
        return self.object_cache['operations'][operation_code]

    def _get_cached_currency(self, currency_code: str) -> Currency:
        """Get currency from cache or database"""
        if currency_code not in self.object_cache['currencies']:
            try:
                currency = Currency.objects.get(currency_code=currency_code)
                self.object_cache['currencies'][currency_code] = currency
            except Currency.DoesNotExist:
                logger.error(f"Currency {currency_code} not found")
                raise
        return self.object_cache['currencies'][currency_code]

    def _get_cached_custodian(self, custodian_code: str) -> Custodian:
        """Get custodian from cache or database"""
        if custodian_code not in self.object_cache['custodians']:
            try:
                custodian = Custodian.objects.get(custodian_code=custodian_code)
                self.object_cache['custodians'][custodian_code] = custodian
            except Custodian.DoesNotExist:
                logger.error(f"Custodian {custodian_code} not found")
                raise
        return self.object_cache['custodians'][custodian_code]

    def _get_cached_partner(self, partner_code: str) -> Partner:
        """Get partner from cache or create if needed"""
        if partner_code not in self.object_cache['partners']:
            partner, created = Partner.objects.get_or_create(
                partner_code=partner_code,
                defaults={'partner_name': partner_code}
            )
            self.object_cache['partners'][partner_code] = partner
            if created:
                logger.debug(f"Created new partner: {partner_code}")
        return self.object_cache['partners'][partner_code]

    def _get_cached_account(self, account_code: str) -> Account:
        """Get account from cache or create if needed"""
        if account_code not in self.object_cache['accounts']:
            # Extract currency from account code (format: CUSTODIAN_CURRENCY)
            currency_code = account_code.split('_')[-1] if '_' in account_code else 'RON'
            currency = self._get_cached_currency(currency_code)

            account, created = Account.objects.get_or_create(
                account_code=account_code,
                defaults={
                    'account_name': f"Account {account_code}",
                    'currency': currency
                }
            )
            self.object_cache['accounts'][account_code] = account
            if created:
                logger.debug(f"Created new account: {account_code}")
        return self.object_cache['accounts'][account_code]

    def _get_cached_instrument(self, instrument_symbol: str, currency: Currency, custodian: Custodian) -> Instrument:
        """Get instrument from cache or create if needed"""
        cache_key = f"{custodian.custodian_code}_{instrument_symbol}"
        if cache_key not in self.object_cache['instruments']:
            # Create ISIN for deposits (truncate to 12 chars if needed)
            deposit_isin = instrument_symbol[:12] if len(instrument_symbol) <= 12 else instrument_symbol[:12]

            instrument, created = Instrument.objects.get_or_create(
                symbol=instrument_symbol,
                custodian=custodian,
                defaults={
                    'name': f"Deposit {instrument_symbol}",
                    'isin': deposit_isin,
                    'currency': currency,
                    'type': 'DEPOSIT',
                    'sector': 'BANKING',
                    'country': 'UNKNOWN'
                }
            )
            self.object_cache['instruments'][cache_key] = instrument
            if created:
                logger.debug(f"Created new instrument: {instrument_symbol}")
        return self.object_cache['instruments'][cache_key]

    def _create_journal_entries(self, accrual_entries: List[Dict], deposit: Deposits) -> int:
        """Create or update journal entries in the database using Django ORM"""
        created_count = 0
        updated_count = 0

        for entry in accrual_entries:
            try:
                # Get or create required objects
                operation = self._get_or_create_operation(entry['operation'])
                instrument = self._get_or_create_instrument(entry['instrument'], deposit)
                account = self._get_or_create_account(entry['account'], entry['currency'])
                partner = self._get_or_create_partner(entry['partner'])
                custodian = Custodian.objects.get(custodian_code=entry['custodian'])

                # Validate that all required objects were created successfully
                if not instrument:
                    raise ValueError(f"Failed to create or find instrument for '{entry['instrument']}'")
                if not operation:
                    raise ValueError(f"Failed to create or find operation for '{entry['operation']}'")
                if not account:
                    raise ValueError(f"Failed to create or find account for '{entry['account']}'")
                if not partner:
                    raise ValueError(f"Failed to create or find partner for '{entry['partner']}'")
                if not custodian:
                    raise ValueError(f"Failed to find custodian for '{entry['custodian']}'")

                # Prepare journal entry data
                journal_data = {
                    'date': entry['date'],
                    'operation': operation,
                    'value': float(entry['value']),
                    'bnr': float(entry['bnr']),
                    'value_ron': float(entry['value_ron']),
                    'quantity': float(entry['quantity']),
                    'ubo': self.ubo,
                    'custodian': custodian,
                    'account': account,
                    'partner': partner,
                    'instrument': instrument,
                    'details': entry['details'],
                    'storno': entry.get('storno', False)
                }

                # Use update_or_create with transactionid as the unique identifier
                journal, created = Journal.objects.update_or_create(
                    transactionid=entry['transactionid'],
                    defaults=journal_data
                )

                if created:
                    created_count += 1
                    logger.debug(f"Created journal entry: {journal.transactionid}")
                else:
                    updated_count += 1
                    logger.debug(f"Updated journal entry: {journal.transactionid}")

            except Exception as e:
                logger.error(f"Error creating/updating journal entry for {entry['transactionid']}: {str(e)}")
                raise

        logger.info(f"Journal entries: {created_count} created, {updated_count} updated")
        return created_count + updated_count

    def _get_or_create_operation(self, operation_code: str) -> Operation:
        """Get or create Operation object"""
        operation, created = Operation.objects.get_or_create(
            operation_code=operation_code,
            defaults={'operation_name': operation_code}
        )
        if created:
            logger.debug(f"Created new operation: {operation_code}")
        return operation

    def _get_or_create_instrument(self, instrument_symbol: str, deposit: Deposits) -> Instrument:
        """Get or create Instrument object following the pattern used by other services"""
        # Try to use the deposit's ID to find the corresponding instrument
        deposit_id = deposit.deposit.id
        if deposit_id:
            try:
                return Instrument.objects.get(id=deposit_id)
            except Instrument.DoesNotExist:
                logger.warning(f"Instrument with ID {deposit_id} not found, will create new one.")

        # First try to find existing instrument with symbol and custodian
        instrument = Instrument.objects.filter(
            symbol=instrument_symbol,
            custodian=deposit.deposit.custodian
        ).first()

        if instrument:
            return instrument

        logger.warning(f"Instrument not found for {instrument_symbol}, creating one.")

        try:
            from django.db import transaction
            with transaction.atomic():
                # Log the data being used for creation
                logger.debug(f"Creating instrument with symbol='{instrument_symbol}', custodian='{deposit.deposit.custodian.custodian_code}', currency='{deposit.deposit.currency.currency_code}'")

                # For deposits, create a truncated ISIN since the field has max 12 chars
                # and deposit symbols can be longer
                deposit_isin = instrument_symbol[:12] if len(instrument_symbol) <= 12 else f"DEP{instrument_symbol[-9:]}"

                instrument, created = Instrument.objects.get_or_create(
                    symbol=instrument_symbol,
                    custodian=deposit.deposit.custodian,
                    defaults={
                        'name': f"Deposit {deposit.deposit.symbol}",
                        'isin': deposit_isin,  # Use truncated/formatted ISIN for deposits
                        'currency': deposit.deposit.currency,
                        'type': 'DEPOSIT',
                        'sector': 'BANKING',  # Default sector for deposits
                        'country': 'UNKNOWN'  # Default country
                    }
                )
                if created:
                    logger.info(f"Created new instrument: '{instrument_symbol}' for custodian '{deposit.deposit.custodian.custodian_code}'.")
                else:
                    logger.debug(f"Found existing instrument: '{instrument_symbol}' for custodian '{deposit.deposit.custodian.custodian_code}'.")
                return instrument
        except Exception as e:
            logger.error(f"Error getting or creating instrument '{instrument_symbol}': {e}", exc_info=True)
            # Fallback: try to get existing instrument in case of race condition
            fallback_instrument = Instrument.objects.filter(
                symbol=instrument_symbol,
                custodian=deposit.deposit.custodian
            ).first()

            if fallback_instrument:
                logger.warning(f"Using fallback instrument for '{instrument_symbol}'")
                return fallback_instrument

            # If still no instrument found, raise an exception rather than returning None
            raise ValueError(f"Could not create or find instrument '{instrument_symbol}' for custodian '{deposit.deposit.custodian.custodian_code}'. Original error: {e}")

    def _get_or_create_account(self, account_code: str, currency_code: str) -> Account:
        """Get or create Account object"""
        account, created = Account.objects.get_or_create(
            account_code=account_code,
            defaults={
                'account_name': f"Account {account_code}",
                'currency': Currency.objects.get(currency_code=currency_code)
            }
        )
        if created:
            logger.debug(f"Created new account: {account_code}")
        return account

    def _get_or_create_partner(self, partner_code: str) -> Partner:
        """Get or create Partner object"""
        partner, created = Partner.objects.get_or_create(
            partner_code=partner_code,
            defaults={'partner_name': partner_code}
        )
        if created:
            logger.debug(f"Created new partner: {partner_code}")
        return partner

    def get_deposit_summary(self, deposit_symbol: str = None) -> Dict:
        """Get summary of deposits and their accruals"""
        deposits_queryset = self._get_deposits_queryset(deposit_symbol)

        summary = {
            'total_deposits': deposits_queryset.count(),
            'deposits': []
        }

        for deposit in deposits_queryset:
            deposit_info = self._extract_deposit_info(deposit)

            # Calculate total expected interest
            days_total = (deposit_info['end_date'] - deposit_info['start_date']).days
            expected_interest = (deposit_info['principal'] *
                               deposit_info['interest_rate'] / 100 *
                               days_total / deposit_info['convention'])

            # Get existing journal entries
            existing_journals = Journal.objects.filter(
                instrument__symbol__contains=deposit_info['deposit_id']
            ).count()

            summary['deposits'].append({
                'symbol': deposit_info['deposit_id'],
                'custodian': deposit_info['custodian'],
                'currency': deposit_info['currency'],
                'principal': deposit_info['principal'],
                'interest_rate': deposit_info['interest_rate'],
                'start_date': deposit_info['start_date'],
                'end_date': deposit_info['end_date'],
                'expected_interest': round(expected_interest, 2),
                'collected_interest': deposit_info['incasat'],
                'existing_journal_entries': existing_journals
            })

        return summary

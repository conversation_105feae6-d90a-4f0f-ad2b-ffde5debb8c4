#!/usr/bin/env python
"""
Test script for accruals optimization performance
"""
import os
import sys
import django
import time
import logging
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nch.settings')
django.setup()

from port.services.provider.deposits_service import DepositsService
from port.services.provider.bonds_service import BondService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_deposits_optimization():
    """Test the optimized deposits service"""
    logger.info("Testing DepositsService optimization...")
    
    try:
        # Initialize service
        service = DepositsService(ubo_id=1)
        
        # Test cache preloading
        start_time = time.time()
        service._preload_caches()
        cache_time = time.time() - start_time
        logger.info(f"Cache preloading took {cache_time:.2f} seconds")
        
        # Test optimized calculation
        start_time = time.time()
        result = service.calculate_and_store_accruals()
        calc_time = time.time() - start_time
        logger.info(f"Optimized calculation took {calc_time:.2f} seconds")
        logger.info(f"Result: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing deposits optimization: {e}", exc_info=True)
        return False

def test_bonds_optimization():
    """Test the optimized bonds service"""
    logger.info("Testing BondService optimization...")
    
    try:
        # Test optimized calculation
        start_time = time.time()
        final_grouped_bonds, count, errors = BondService.calculate_bond_accruals()
        calc_time = time.time() - start_time
        
        logger.info(f"Optimized bond calculation took {calc_time:.2f} seconds")
        logger.info(f"Processed {count} bond entries with {len(errors)} errors")
        
        if errors:
            logger.warning(f"Errors: {errors}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing bonds optimization: {e}", exc_info=True)
        return False

def test_cache_performance():
    """Test cache performance improvements"""
    logger.info("Testing cache performance...")
    
    try:
        service = DepositsService(ubo_id=1)
        
        # Test BNR rate lookup performance
        from datetime import date
        test_date = date(2024, 1, 15)
        
        # Time cached lookup
        start_time = time.time()
        for _ in range(100):
            rate = service._get_cached_bnr_rate('USD', test_date)
        cached_time = time.time() - start_time
        
        logger.info(f"100 cached BNR lookups took {cached_time:.4f} seconds")
        logger.info(f"Average per lookup: {cached_time/100:.6f} seconds")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing cache performance: {e}", exc_info=True)
        return False

def main():
    """Run all optimization tests"""
    logger.info("Starting accruals optimization tests...")
    logger.info("=" * 60)
    
    results = {
        'deposits': test_deposits_optimization(),
        'bonds': test_bonds_optimization(), 
        'cache': test_cache_performance()
    }
    
    logger.info("=" * 60)
    logger.info("Test Results:")
    for test_name, success in results.items():
        status = "PASSED" if success else "FAILED"
        logger.info(f"  {test_name.capitalize()}: {status}")
    
    overall_success = all(results.values())
    logger.info(f"Overall: {'PASSED' if overall_success else 'FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
